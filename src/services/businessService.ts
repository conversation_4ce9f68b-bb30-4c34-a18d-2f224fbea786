import { ApiResponse, apiService } from './api';
import { BusinessType } from '@/pages/Business';

// Business interfaces
export interface BusinessResponse {
  data: BusinessType[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface BusinessLinkRequest {
  provider_id: string;
  business_uuid: string;
}

export interface BusinessLinkResponse {
  success: boolean;
  message: string;
}

// Business service functions
export const businessService = {
  // Get all businesses with pagination
  getBusinesses: async (
    page: number = 1,
    perPage: number = 10,
    search?: string,
    zipCode?: string,
    token?: string
  ): Promise<ApiResponse<BusinessResponse>> => {
    let endpoint = `/api/businesses?page=${page}&per_page=${perPage}`;

    if (search) {
      endpoint += `&search=${encodeURIComponent(search)}`;
    }

    if (zipCode) {
      endpoint += `&zip_code=${encodeURIComponent(zipCode)}`;
    }

    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<BusinessResponse>(endpoint, {
      method: 'GET',
      headers,
      requiresAuth: true,
    });
  },

  // Get a single business by ID
  getBusiness: async (id: string, token?: string): Promise<ApiResponse<BusinessType>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<BusinessType>(`/api/businesses/${id}`, {
      method: 'GET',
      headers,
      requiresAuth: true,
    });
  },

  // Create a new business
  createBusiness: async (data: Partial<BusinessType>, token?: string): Promise<ApiResponse<BusinessType>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<BusinessType>('/api/businesses', {
      method: 'POST',
      body: data,
      headers,
      requiresAuth: true,
    });
  },

  // Update an existing business
  updateBusiness: async (id: string, data: Partial<BusinessType>, token?: string): Promise<ApiResponse<BusinessType>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<BusinessType>(`/api/businesses/${id}`, {
      method: 'PUT',
      body: data,
      headers,
      requiresAuth: true,
    });
  },

  // Delete a business
  deleteBusiness: async (id: string, token?: string): Promise<ApiResponse<void>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<void>(`/api/businesses/${id}`, {
      method: 'DELETE',
      headers,
      requiresAuth: true,
    });
  },

  // Link a business to a provider
  linkBusinessToProvider: async (data: BusinessLinkRequest, token?: string): Promise<ApiResponse<BusinessLinkResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<BusinessLinkResponse>('/api/admin/provider/link-business', {
      method: 'POST',
      body: data,
      headers,
      requiresAuth: true,
    });
  },

  // Unlink a business from a provider
  unlinkBusinessFromProvider: async (data: BusinessLinkRequest, token?: string): Promise<ApiResponse<BusinessLinkResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<BusinessLinkResponse>('/api/provider/unlink-business', {
      method: 'POST',
      body: data,
      headers,
      requiresAuth: true,
    });
  },

  // Get business linked to a provider
  getProviderBusinesses: async (providerId: string, token?: string): Promise<ApiResponse<BusinessType>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<BusinessType>(`/api/admin/provider/business/${providerId}`, {
      method: 'GET',
      headers,
      requiresAuth: true,
    });
  }
};