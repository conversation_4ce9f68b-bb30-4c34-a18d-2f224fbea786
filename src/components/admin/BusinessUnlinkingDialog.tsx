import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Loader2, Building2, Link2Off, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import { nullToUndefined } from '@/utils/typeHelpers';
import { businessService } from '@/services/businessService';
import { BusinessType } from '@/pages/Business';
import { Provider } from '@/services/providerService';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertD<PERSON>ogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

interface BusinessUnlinkingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  provider?: Provider | null;
  onSuccess?: () => void;
}

const BusinessUnlinkingDialog: React.FC<BusinessUnlinkingDialogProps> = ({
  isOpen,
  onClose,
  provider,
  onSuccess
}) => {
  const [linkedBusinesses, setLinkedBusinesses] = useState<BusinessType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isUnlinking, setIsUnlinking] = useState(false);
  const [confirmUnlinkOpen, setConfirmUnlinkOpen] = useState(false);
  const [businessToUnlink, setBusinessToUnlink] = useState<BusinessType | null>(null);
  const authHeader = useAuthHeader();

  // Fetch linked business for the provider
  useEffect(() => {
    if (!isOpen || !provider) return;
    
    const fetchLinkedBusiness = async () => {
      setIsLoading(true);
      try {
        const response = await businessService.getProviderBusinesses(
          provider.id,
          nullToUndefined(authHeader) || ''
        );
        
        if (response.isSuccess && response.data) {
          // Parse the JSON strings in the response data
          const parsedBusiness = {
            ...response.data,
            photos: typeof response.data.photos === 'string' ? 
              JSON.parse(response.data.photos) : response.data.photos,
            hours: typeof response.data.hours === 'string' ? 
              JSON.parse(response.data.hours) : response.data.hours,
            reviews: typeof response.data.reviews === 'string' ? 
              JSON.parse(response.data.reviews) : response.data.reviews,
            services: typeof response.data.services === 'string' ? 
              JSON.parse(response.data.services) : response.data.services
          };
          
          // API now returns a single business object instead of an array
          setLinkedBusinesses([parsedBusiness.dat]);
          console.log('Parsed business data:', parsedBusiness);
        } else if (response.error === 'Provider is not linked to any business') {
          // Handle specific error case when provider is not linked to any business
          setLinkedBusinesses([]);
        } else if (response.error === 'Business not found') {
          // Handle specific error case when business is not found
          setLinkedBusinesses([]);
          toast.error('Business not found');
        } else if (response.error?.includes('No query results for model')) {
          // Handle specific error case when provider is not found
          setLinkedBusinesses([]);
          toast.error('Provider not found');
        } else {
          toast.error(response.error || 'Failed to fetch linked business');
          setLinkedBusinesses([]);
        }
      } catch (error) {
        console.error('Error fetching linked business:', error);
        toast.error('Failed to fetch linked business');
        setLinkedBusinesses([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLinkedBusiness();
  }, [isOpen, provider, authHeader]);

  // Open confirmation dialog before unlinking
  const openUnlinkConfirmation = (business: BusinessType) => {
    setBusinessToUnlink(business);
    setConfirmUnlinkOpen(true);
  };

  // Handle unlinking a business from a provider
  const handleUnlink = async () => {
    if (!provider || !businessToUnlink) return;
    
    setIsUnlinking(true);
    try {
      const data = {
        provider_id: provider.id,
        business_uuid: businessToUnlink.businessId
      };
      
      const response = await businessService.unlinkBusinessFromProvider(
        data,
        nullToUndefined(authHeader) || ''
      );
      
      if (response.isSuccess) {
        // Update the linked businesses list
        setLinkedBusinesses(prev => prev.filter(b => b.businessId !== businessToUnlink.businessId));
        toast.success(`Unlinked ${businessToUnlink.name} from ${provider.name}`);
        
        // Call the onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast.error(response.error || 'Failed to unlink business');
      }
    } catch (error) {
      console.error('Error unlinking business:', error);
      toast.error('Failed to unlink business');
    } finally {
      setIsUnlinking(false);
      setConfirmUnlinkOpen(false);
      setBusinessToUnlink(null);
    }
  };

  // Get business initials for avatar fallback
  const getBusinessInitials = (name: string | undefined | null): string => {
    if (!name) return '?';
    
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name.charAt(0).toUpperCase();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {provider ? `Unlink Business from ${provider.name}` : 'Unlink Business'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Linked businesses list */}
          {provider ? (
            <div className="border rounded-md">
              <div className="p-3 border-b">
                <h3 className="font-medium">Business linked to {provider.name}</h3>
              </div>
              <ScrollArea className="h-[350px] w-full">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full p-4">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading linked business...</span>
                  </div>
                ) : linkedBusinesses.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full p-4 text-center">
                    <Building2 className="h-10 w-10 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">No business linked to this provider</p>
                  </div>
                ) : (
                  <div className="space-y-2 p-2">
                    {linkedBusinesses.map((business) => (
                      <div
                        key={business.businessId}
                        className="flex items-center justify-between p-3 rounded-md border hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage 
                              src={Array.isArray(business.photos) && business.photos.length > 0 ? business.photos[0] : undefined} 
                              alt={business.name || 'Business'} 
                            />
                            <AvatarFallback>{getBusinessInitials(business.name)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{business.name || 'Unnamed Business'}</p>
                            <p className="text-sm text-muted-foreground">{business.category || 'No category'}</p>
                          </div>
                        </div>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => openUnlinkConfirmation(business)}
                          disabled={isUnlinking}
                        >
                          <Link2Off className="h-4 w-4 mr-1" />
                          Unlink
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <AlertCircle className="h-10 w-10 text-muted-foreground mb-2" />
              <p className="text-muted-foreground">No provider selected</p>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmUnlinkOpen} onOpenChange={setConfirmUnlinkOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 text-destructive mr-2" />
              Confirm Unlinking
            </AlertDialogTitle>
            <AlertDialogDescription>
              {businessToUnlink && provider && (
                <div className="space-y-4 py-2">
                  <p>Are you sure you want to unlink this business from the provider?</p>
                  
                  <div className="bg-muted p-4 rounded-md">
                    <h4 className="font-medium mb-2">Relationship Details:</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="text-muted-foreground">Provider:</p>
                        <div className="flex items-center mt-1">
                          <Avatar className="h-6 w-6 mr-2">
                            <AvatarImage src={provider.avatar || "/placeholder.svg"} alt={provider.name} />
                            <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{provider.name}</span>
                        </div>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Business:</p>
                        <div className="flex items-center mt-1">
                          <Avatar className="h-6 w-6 mr-2">
                            <AvatarImage 
                              src={businessToUnlink && Array.isArray(businessToUnlink.photos) && businessToUnlink.photos.length > 0 ? businessToUnlink.photos[0] : undefined} 
                              alt={businessToUnlink?.name || 'Business'} 
                            />
                            <AvatarFallback>{getBusinessInitials(businessToUnlink?.name)}</AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{businessToUnlink?.name || 'Unnamed Business'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-destructive text-sm">
                    This action cannot be undone. The provider will no longer be associated with this business.
                  </p>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isUnlinking}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleUnlink();
              }}
              disabled={isUnlinking}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isUnlinking ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Unlinking...
                </>
              ) : (
                'Confirm Unlink'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Dialog>
  );
};

export default BusinessUnlinkingDialog;